const Joi = require("joi");
const models = require("../models");

const { exists, unique } = require("./custom.validation");

const badgeId = Joi.string().required().external(exists("Badge", "badge_id"));

const schemas = Object.keys(models).filter(key =>
  key !== 'sequelize' && key !== 'Sequelize'
);

const BadgeValidation = {
  create: {
    body: Joi.object().keys({
      name: Joi.string().required().max(255).external(unique("Badge", "name")),
      status: Joi.boolean().optional().default(true),
      images: Joi.array().items(Joi.string().uri()).optional().allow(null),
      content: Joi.object().optional().allow(null),
      variables: Joi.array().optional().allow(null),
      schema: Joi.string().required().valid(...schemas).max(255),
      key: Joi.string().required().max(255).external(async (value, helpers) => {
        const { schema } = helpers.state.ancestors[0];
        if (!models[schema]) throw new Error(`Schema "${schema}" is not a valid model.`);
        const attrs = Object.values(models[schema].rawAttributes).map(attr => attr.fieldName);
        if (!attrs.includes(value)) {
          throw new Error(
            `Key "${value}" does not exist on model "${schema}".`
          );
        }
        return value;
      }),
      format: Joi.string().valid('PNG', 'JPEG', 'JPG', 'WEBP', 'BMP', 'TIFF', 'PDF').required(),
    }),
  },

  update: {
    params: Joi.object().keys({
      badgeId,
    }),
    body: Joi.object()
      .keys({
        name: Joi.string().optional().max(255),
        status: Joi.boolean().optional().default(true),
        images: Joi.array().items(Joi.string().uri()).optional().allow(null),
        content: Joi.string()
          .optional()
          .custom((value, helpers) => {
            try {
              return JSON.parse(value);
            } catch (err) {
              return helpers.error('any.invalid');
            }
          })
          .message('"content" must be a valid JSON string'),
        variables: Joi.string()
          .optional()
          .custom((value, helpers) => {
            try {
              const parsed = JSON.parse(value);
              if (!Array.isArray(parsed)) {
                throw new Error();
              }
              return parsed;
            } catch (err) {
              return helpers.error('any.invalid');
            }
          })
          .message('"variables" must be a valid JSON array string'),
        schema: Joi.string().valid().optional().valid(...schemas).max(255),
        key: Joi.string().optional().max(255).external(async (value, helpers) => {
          const { key, schema } = helpers.state.ancestors[0];
          if (!key && !schema) {
            return value;
          }
          const model = models[schema];
          if (!model) {
            throw new Error(`Schema "${schema}" is not a valid model.`);
          }
          const attrs = Object.values(model.rawAttributes).map(
            (attr) => attr.fieldName
          );
          if (!attrs.includes(value)) {
            throw new Error(
              `Key "${value}" does not exist on model "${schema}".`
            );
          }

          return value;
        }),
        format: Joi.string().valid('PNG', 'JPEG', 'JPG', 'WEBP', 'BMP', 'TIFF', 'PDF').optional(),
      }),
  },

  badge: {
    params: Joi.object().keys({
      badgeId,
    }),
  },

  index: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).default(1).optional(),
      limit: Joi.number().integer().min(1).max(100).default(10).optional(),
      sortBy: Joi.string().valid("name", "status", "created_at", "updated_at").default("updated_at").optional(),
      sortOrder: Joi.string().valid("asc", "desc").default("desc").optional(),
      search: Joi.string().allow("").optional(),
    }),
  },

  delete: {
    params: Joi.object().keys({
      badgeId,
    }),
  },

  print: {
    body: Joi.object().keys({
      badge_id: Joi.string().required().external(exists("Badge", "badge_id")),
      instance_id: Joi.string().required(),
    }),
  },

  modelAttributes: {
    params: Joi.object().keys({
      modelName: Joi.string().required().max(255),
    }),
  },
};

module.exports = BadgeValidation;
