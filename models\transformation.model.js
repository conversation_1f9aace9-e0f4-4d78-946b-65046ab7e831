const { DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  const Transformation = sequelize.define(
    "Transformation",
    {
      transformation_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      queue_name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      function_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      display_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      }
    },
    {
      tableName: "transformations",
      timestamps: true,
      underscored: true,
    }
  );

  return Transformation;
};