const { MEDIA } = require("../../config/attributes");
const sharp = require("sharp");
const logger = require("../../config/logger");

/**
 * Checks if the base64 data represents an image
 *
 * @param {string} base64data - Base64-encoded data (may include `data:*;base64,` prefix).
 * @returns {boolean} - True if the data is an image, false otherwise.
 */
function isImage(base64data) {
  if (!base64data) return false;

  // Check if it has a data URI prefix with image MIME type
  if (base64data.startsWith('data:image/')) {
    return true;
  }

  // If no prefix, try to detect by content (basic check)
  try {
    const clean = base64data.replace(/^data:[^;]+;base64,/, "");
    const buf = Buffer.from(clean, "base64");

    // Check for common image file signatures
    const signatures = [
      [0xFF, 0xD8, 0xFF], // JPEG
      [0x89, 0x50, 0x4E, 0x47], // PNG
      [0x47, 0x49, 0x46], // GIF
      [0x52, 0x49, 0x46, 0x46], // WEBP (starts with RIFF)
      [0x42, 0x4D], // BMP
    ];

    return signatures.some(sig =>
      sig.every((byte, index) => buf[index] === byte)
    );
  } catch (err) {
    return false;
  }
}

/**
 * Generates a resized thumbnail (max 200 width) from a base64-encoded image.
 * Only creates thumbnails for image files.
 *
 * @param {string} base64data - Base64-encoded image data (may include `data:*;base64,` prefix).
 * @returns {Promise<string|null>} - Full `data:image/avif;base64,…` thumbnail URL, or null if generation fails or not an image.
 */
async function createThumbnail(base64data) {
  if (!base64data) return null;

  // Only create thumbnails for images
  if (!isImage(base64data)) {
    logger.info("Skipping thumbnail generation for non-image file");
    return null;
  }

  try {
    // strip off any data URI prefix, e.g. "data:image/png;base64,"
    const clean = base64data.replace(/^data:[^;]+;base64,/, "");
    const buf   = Buffer.from(clean,      "base64");
    const thumbBuf = await sharp(buf)
      .resize({ width: 200 })
      .avif({ quality: 50 })        // always output as AVIF
      .toBuffer();
      const b64 = thumbBuf.toString("base64");
      return `data:image/avif;base64,${b64}`;  // ← full data-URI URL
  } catch (err) {
    logger.error("Thumbnail generation failed:", err);
    return null;
  }
}

/**
 * Revised Media Plugin:
 *
 * - Scans the parent model's attributes for any field using the custom MEDIA type.
 * - For each such field, it removes the original definition and re-adds it as a foreign key (UUID) field.
 * - It creates a single media model/table with 3 columns: id (UUID), key, and value.
 * - In the hooks it creates (afterCreate/afterUpdate) or updates a media record with:
 *     { key: <field name>, value: <provided value> }
 * - Associations are set so that the parent “belongsTo” the media record (aliased e.g. as "imageMedia").
 */
module.exports = (parentModel, sequelize, DataTypes) => {
  const parentTableName = parentModel.getTableName();

  // 1. Scan parent's rawAttributes for media fields.
  const mediaFieldsConfig = {};
  Object.keys(parentModel.rawAttributes).forEach((fieldName) => {
    const attribute = parentModel.rawAttributes[fieldName];
    if (attribute.type === MEDIA) {
      mediaFieldsConfig[fieldName] = {
        allowMultiple: attribute.allowMultiple || false,
        allowNull: attribute.allowNull,
      };
    }
  });

  // If no media fields are found, exit the plugin.
  if (Object.keys(mediaFieldsConfig).length === 0) {
    return null;
  }

  // 2. Define custom getters/setters on the parent model.
  Object.keys(mediaFieldsConfig).forEach((fieldName) => {
    Object.defineProperty(parentModel.prototype, fieldName, {
      set(value) {
        // Store in temporary container for the hook
        this._tempMediaValues = this._tempMediaValues || {};
        this._tempMediaValues[fieldName] = value;
        this.setDataValue(fieldName, value);
      },
      get() {
        return this.getDataValue(fieldName);
      },
      configurable: true,
    });

    // Add a method to get the actual media data for multiple images
    const fieldConfig = mediaFieldsConfig[fieldName];
    if (fieldConfig.allowMultiple) {
      const getMediaMethodName = `get${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}Media`;
      parentModel.prototype[getMediaMethodName] = async function(options = {}) {
        const mediaIds = this.getDataValue(fieldName);
        if (!mediaIds) return [];

        try {
          const ids = JSON.parse(mediaIds);
          if (!Array.isArray(ids)) return [];

          const mediaRecords = await MediaModel.findAll({
            where: { [parentTableName + "_media_id"]: ids },
            transaction: options.transaction
          });

          return mediaRecords;
        } catch (err) {
          logger.error(`Error fetching media for ${fieldName}:`, err);
          return [];
        }
      };
    }
  });

  // 3. Add a hook to copy temp values into _mediaForHook and clear the actual field value.
  parentModel.addHook("beforeValidate", (instance, options) => {
    instance._tempMediaValues = instance._tempMediaValues || {};
    if (Object.keys(instance._tempMediaValues).length > 0) {
      instance._mediaForHook = { ...instance._tempMediaValues };
    }
    Object.keys(mediaFieldsConfig).forEach((fieldName) => {
      instance.setDataValue(fieldName, null);
    });
  });

  // 4. Update parent's attributes: Remove the original media attribute and re-add it as a foreign key (UUID) or JSON for multiple.
  Object.keys(mediaFieldsConfig).forEach((fieldName) => {
    const originalDefinition = { ...parentModel.rawAttributes[fieldName] };
    const fieldConfig = mediaFieldsConfig[fieldName];
    delete parentModel.rawAttributes[fieldName];

    parentModel.rawAttributes[fieldName] = {
      type: fieldConfig.allowMultiple ? DataTypes.TEXT : DataTypes.UUID, // TEXT for JSON array, UUID for single
      allowNull: originalDefinition.allowNull,
      set(value) {
        this._tempMediaValues = this._tempMediaValues || {};
        this._tempMediaValues[fieldName] = value;
        this.setDataValue(fieldName, value);
      },
      get() {
        return this.getDataValue(fieldName);
      },
    };
  });
  parentModel.refreshAttributes();

  // 5. Create the generic media model with two columns: key and value.
  const mediaModelName = parentTableName + "_media";
  const MediaModel = sequelize.define(
    mediaModelName,
    {
      [parentTableName + "_media_id"]: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      key: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      value: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      thumbnail: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "Base64-encoded thumbnail",
      },
    },
    {
      tableName: mediaModelName,
      timestamps: true,
      underscored: true,
    }
  );

  // 6. After Create Hook: create media records for each media field.
  parentModel.addHook("afterCreate", async (instance, options) => {
    const mediaValues = instance._mediaForHook || instance._tempMediaValues;
    for (const fieldName of Object.keys(mediaFieldsConfig)) {
      if (mediaValues && mediaValues[fieldName] !== undefined) {
        const fieldConfig = mediaFieldsConfig[fieldName];
        const value = mediaValues[fieldName];

        if (fieldConfig.allowMultiple && Array.isArray(value)) {
          // Handle multiple images - create multiple media records
          const mediaRecordIds = [];
          for (const singleValue of value) {
            const thumb = await createThumbnail(singleValue);
            const mediaRecord = await MediaModel.create(
              {
                key: fieldName,
                value: singleValue,
                thumbnail: thumb
              },
              { transaction: options.transaction }
            );
            mediaRecordIds.push(mediaRecord[parentTableName + "_media_id"]);
          }
          // Store array of media record IDs as JSON
          instance.setDataValue(fieldName, JSON.stringify(mediaRecordIds));
        } else {
          // Handle single image - create single media record
          const thumb = await createThumbnail(value);
          const mediaRecord = await MediaModel.create(
            {
              key: fieldName,
              value: value,
              thumbnail: thumb
            },
            { transaction: options.transaction }
          );
          // Store single media record ID
          instance.setDataValue(fieldName, mediaRecord[parentTableName + "_media_id"]);
        }

        await instance.save({ transaction: options.transaction, hooks: false });
        if (instance._mediaForHook) {
          delete instance._mediaForHook[fieldName];
        }
      }
    }
  });

  // 7. After Update Hook: update or create media records as needed.
  parentModel.addHook("afterUpdate", async (instance, options) => {
    const mediaValues = instance._mediaForHook || instance._tempMediaValues;
    for (const fieldName of Object.keys(mediaFieldsConfig)) {
      if (mediaValues && mediaValues[fieldName] !== undefined) {
        const fieldConfig = mediaFieldsConfig[fieldName];
        const value = mediaValues[fieldName];
        const currentMediaData = instance.getDataValue(fieldName);

        if (fieldConfig.allowMultiple && Array.isArray(value)) {
          // Handle multiple images - delete existing records and create new ones
          if (currentMediaData) {
            try {
              const existingIds = JSON.parse(currentMediaData);
              if (Array.isArray(existingIds)) {
                // Delete existing media records
                await MediaModel.destroy({
                  where: { [parentTableName + "_media_id"]: existingIds },
                  transaction: options.transaction
                });
              }
            } catch (err) {
              // If parsing fails, try to delete single record
              await MediaModel.destroy({
                where: { [parentTableName + "_media_id"]: currentMediaData },
                transaction: options.transaction
              });
            }
          }

          // Create new media records
          const mediaRecordIds = [];
          for (const singleValue of value) {
            const thumb = await createThumbnail(singleValue);
            const mediaRecord = await MediaModel.create(
              {
                key: fieldName,
                value: singleValue,
                thumbnail: thumb
              },
              { transaction: options.transaction }
            );
            mediaRecordIds.push(mediaRecord[parentTableName + "_media_id"]);
          }
          // Store array of media record IDs as JSON
          instance.setDataValue(fieldName, JSON.stringify(mediaRecordIds));
        } else {
          // Handle single image - update or create single media record
          let mediaRecord;
          if (currentMediaData) {
            mediaRecord = await MediaModel.findOne({
              where: { [parentTableName + "_media_id"]: currentMediaData },
              transaction: options.transaction,
            });
          }

          const thumb = await createThumbnail(value);
          if (mediaRecord) {
            mediaRecord.value = value;
            mediaRecord.thumbnail = thumb;
            await mediaRecord.save({ transaction: options.transaction });
          } else {
            mediaRecord = await MediaModel.create(
              {
                key: fieldName,
                value: value,
                thumbnail: thumb
              },
              { transaction: options.transaction }
            );
            instance.setDataValue(fieldName, mediaRecord[parentTableName + "_media_id"]);
          }
        }

        await instance.save({ transaction: options.transaction, hooks: false });
        if (instance._mediaForHook) {
          delete instance._mediaForHook[fieldName];
        }
      }
    }
  });

  // 8. Set up associations so that the parent can include the media record(s).
  Object.keys(mediaFieldsConfig).forEach((fieldName) => {
    const fieldConfig = mediaFieldsConfig[fieldName];
    const associationAlias = fieldName + "Media";

    if (fieldConfig.allowMultiple) {
      // For multiple images, we'll handle the association manually in queries
      // since we store JSON array of IDs rather than direct foreign keys
      // The association will be resolved in custom getter methods
    } else {
      // For single images, maintain the existing association pattern
      parentModel.belongsTo(MediaModel, {
        foreignKey: fieldName,
        as: associationAlias,
        onDelete: "CASCADE",
      });
      MediaModel.hasOne(parentModel, {
        foreignKey: fieldName,
        as: parentTableName + "_" + fieldName,
      });
    }
  });

  return MediaModel;
};
