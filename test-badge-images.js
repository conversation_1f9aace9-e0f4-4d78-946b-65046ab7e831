const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

// Test configuration
const API_BASE_URL = 'http://localhost:3002/api';
const TEST_IMAGE_PATH = path.join(__dirname, 'test-image.png');

// Create a simple test image (1x1 pixel PNG)
const createTestImage = () => {
  const pngData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x01, // Width: 1
    0x00, 0x00, 0x00, 0x01, // Height: 1
    0x08, 0x02, 0x00, 0x00, 0x00, // Bit depth, color type, compression, filter, interlace
    0x90, 0x77, 0x53, 0xDE, // CRC
    0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // Image data
    0xE2, 0x21, 0xBC, 0x33, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);
  
  fs.writeFileSync(TEST_IMAGE_PATH, pngData);
  return TEST_IMAGE_PATH;
};

// Test function
async function testBadgeImageUpload() {
  console.log('🧪 Testing Badge Image Upload API...\n');
  
  try {
    // Create test image
    console.log('📸 Creating test image...');
    createTestImage();
    
    // First, create a badge to test with
    console.log('🏷️  Creating test badge...');
    const createResponse = await axios.post(`${API_BASE_URL}/badges`, {
      name: 'Test Badge',
      status: true,
      schema: 'test-schema',
      key: 'test-key',
      format: 'PNG',
      content: {
        elements: [
          {
            id: 'test-image',
            type: 'image',
            x: 10,
            y: 10,
            width: 100,
            height: 100
          }
        ]
      }
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const badgeId = createResponse.data.data.badge_id;
    console.log(`✅ Badge created with ID: ${badgeId}`);
    
    // Now test image upload via update
    console.log('📤 Testing image upload via badge update...');
    
    const formData = new FormData();
    formData.append('name', 'Updated Test Badge');
    formData.append('images', fs.createReadStream(TEST_IMAGE_PATH), {
      filename: 'test-image.png',
      contentType: 'image/png'
    });
    
    const updateResponse = await axios.patch(`${API_BASE_URL}/badges/${badgeId}`, formData, {
      headers: {
        ...formData.getHeaders()
      }
    });
    
    console.log('✅ Badge updated successfully!');
    console.log('📊 Response data:', JSON.stringify(updateResponse.data, null, 2));
    
    // Verify the badge was updated
    const getResponse = await axios.get(`${API_BASE_URL}/badges/${badgeId}`);
    console.log('🔍 Retrieved badge data:', JSON.stringify(getResponse.data, null, 2));
    
    // Clean up
    console.log('🧹 Cleaning up...');
    await axios.delete(`${API_BASE_URL}/badges/${badgeId}`);
    fs.unlinkSync(TEST_IMAGE_PATH);
    
    console.log('🎉 Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    // Clean up on error
    if (fs.existsSync(TEST_IMAGE_PATH)) {
      fs.unlinkSync(TEST_IMAGE_PATH);
    }
    
    process.exit(1);
  }
}

// Check if server is running
async function checkServer() {
  try {
    await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ Server is running\n');
    return true;
  } catch (error) {
    console.log('❌ Server is not running. Please start the server first.\n');
    return false;
  }
}

// Main execution
async function main() {
  console.log('🚀 Badge Image Upload Test\n');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('Please run: node server.js');
    process.exit(1);
  }
  
  await testBadgeImageUpload();
}

if (require.main === module) {
  main();
}

module.exports = { testBadgeImageUpload, checkServer };
