# Use official Node.js 18 Alpine image as base
FROM node:18-alpine

# Set working directory
WORKDIR /app
USER root

RUN apk add --no-cache git openssh-client 
ARG CI_JOB_TOKEN
RUN git clone "https://gitlab-ci-token:${CI_JOB_TOKEN}@git.onetalkhub.com/care/caremate.git" && \
    cd caremate && \
    git checkout develop

# # Copy package.json and package-lock.json first to leverage Docker cache
# COPY package*.json ./

WORKDIR /app/caremate

RUN apk add --no-cache python3 make g++ pkgconf \
    cairo-dev pango-dev libjpeg-turbo-dev giflib-dev pixman-dev && \
    ln -sf python3 /usr/bin/python

# # Install dependencies
RUN npm install

# # Copy the rest of the project files
RUN rm -rf api
RUN mkdir api
COPY . api/
# # COPY .env.local .env.local

# # Clone the API repository using the CI_JOB_TOKEN
# # This requires the CI_JOB_TOKEN variable to be passed during the build


# # Set environment variable for env file
# ENV ENV_FILE=.env.local

# # Run setup script to create symlinks
RUN npm run setup

RUN cd api && \
    npm install

# Expose the port the app runs on
EXPOSE 3001

# Start the backend server
# CMD ["sh", "-c", "sleep 5m"]
CMD ["sh","-c", "cd api && npm run dev"]