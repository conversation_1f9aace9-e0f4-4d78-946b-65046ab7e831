const DTL = require('dtl-js');
const { DataTypes } = require("sequelize");
const models = require("../models");
const { getCachedMasterDataValue } = require("../helpers/caching.helper");
const { ADT_TYPE_STATUS_MAP, SIU_TYPE_STATUS_MAP } = require(`${process.cwd()}/mappings/hl7TypeStatus.mapping.json`);

const { applyHL7DateParsing, resolveMasterDataFields, cleanDTLResult } = require('../helpers/dtl.helpers')(parseHL7Date);
/**
 * Parse an HL7 date or datetime field:
 *  - YYYYMMDD           → Date at midnight
 *  - YYYYMMDDhhmmss     → Date with time
 */
function parseHL7Date(str) {
  const year = +str.slice(0, 4);
  const month = +str.slice(4, 6) - 1;
  const day = +str.slice(6, 8);

  // If no time part, just use midnight of that day
  if (str.length === 8) {
    return new Date(year, month, day);
  }

  // If there's a full hhmmss, parse those too
  const hour = +str.slice(8, 10);
  const minute = +str.slice(10, 12);
  const second = +str.slice(12, 14);

  return new Date(year, month, day, hour, minute, second);
}

// Map model‐name strings to actual Sequelize model classes
// Helper: check key is of type date or not
function isDateType(dataType) {
  // In most Sequelize versions, key lives on the constructor:
  const key =
    dataType.key || (dataType.constructor && dataType.constructor.key);
  return (
    key === DataTypes.DATE.key || // “DATE”
    key === DataTypes.DATEONLY.key
  ); // “DATEONLY”
}

// Build a “spec” by reading your JSON + looking at each field’s DataType
function buildSpec(mapping) {
  const spec = {};

  for (let [hl7Key, mapVal] of Object.entries(mapping)) {
    if (!mapVal) continue;
    const [modelName, fieldName] = mapVal.split(".");
    const model = models[modelName];
    if (!model) throw new Error(`Unknown model "${modelName}"`);

    spec[modelName] = spec[modelName] || {};
    const entry = (spec[modelName][fieldName] ||= { //1
      keys: [],
      parser: null,
      group: null,
    });
    entry.keys.push(hl7Key);

    const attr = model.rawAttributes[fieldName];
    if (!entry.parser && attr && isDateType(attr.type)) { //2
      entry.parser = parseHL7Date;
    }

    // Check if this field is a master data linked field
    const masterAssociation = Object.values(model.associations).find( //3
      (assoc) =>
        assoc.foreignKey === fieldName && assoc.scope && assoc.scope.group
    );

    if (masterAssociation) {
      entry.group = masterAssociation.scope.group; // Attach group name
    }
  }

  return spec;
}

// Generic builder that uses your spec to turn cleaned HL7 → payload
async function buildPayload(cleaned, fieldSpec, modelName) {
  const model = models[modelName];
  const payload = {};

  for (const [field, { keys, parser, group }] of Object.entries(fieldSpec)) {
    const vals = keys
      .map((k) => cleaned[k])
      .filter((v) => v != null && v !== "");

    if (!vals.length) continue;

    const raw = vals.length > 1 ? vals.join(" ") : vals[0];
    let value = parser ? parser(raw) : raw;

    if (group) {
      // Lookup MasterData key using cache
      const key = await getCachedMasterDataValue(group, raw);
      if (key !== null) {
        value = key;
      } else {
        // fallback to model's default value if MasterData entry not found
        const attr = model.rawAttributes[field];
        if (attr) {
          if (attr.defaultValue !== undefined) {
            value = attr.defaultValue;
          } else if (attr.default !== undefined) {
            value = attr.default;
          }
        }
      }
    }

    payload[field] = value;
  }

  return payload;
}

/**
 * Turn a raw HL7 message (pipe|^/&-delimited) into
 * an object like { 'PID.3.1': '10006579', 'PID.5': 'Smith', … }
 * ALWAYS split every field into components, even if there's no caret (`^`).
 */
function parseHL7Message(message) {
  const cleaned = {};
  const segments = message.trim().split(/\r?\n/).filter(Boolean);

  for (const line of segments) {
    // grab the 3-char segment name
    const segName = line.substr(0, 3);

    // build a fields[] array where fields[0] is the HL7 field-1, fields[1] ⇒ field-2, etc.
    let fields;
    if (segName === "MSH") {
      // MSH-1 is the 4th character of the line (the field separator)
      const fieldSep = line.charAt(3);
      // split by that exact character
      const parts = line.split(fieldSep);
      // parts[0] === "MSH", parts[1] === encoding chars, parts[2] === sending app, …
      // re-insert the separator itself as field-1
      fields = [fieldSep, ...parts.slice(1)];
    } else {
      // for everything else, HL7 always uses "|" as the separator
      fields = line.split("|").slice(1);
    }

    // now fields[0] → segName.1, fields[1] → segName.2, etc.
    fields.forEach((rawField, fi) => {
      const fieldPos = fi + 1; // HL7 field number
      const repetitionKey = `${segName}.${fieldPos}`;

      rawField
        .split("~") // repetitions
        .forEach((rep, rIdx) => {
          const repPrefix = repetitionKey + (rIdx ? `.${rIdx + 1}` : "");
          cleaned[repPrefix] = rep;

          // always split into components, even if no "^"
          rep.split("^").forEach((comp, ci) => {
            const compPrefix = `${repPrefix}.${ci + 1}`;
            if (!comp.includes("&")) {
              cleaned[compPrefix] = comp;
            } else {
              // sub-components
              comp.split("&").forEach((sub, si) => {
                cleaned[`${compPrefix}.${si + 1}`] = sub;
              });
            }
          });
        });
    });
  }

  return cleaned;
}

// Add DTL transformation function
async function transformWithDTL(cleaned, dtlTemplate, groupMap, typeOfFunction) {
  try {
    const dtl = new DTL(); // Instantiate DTL

    // Step 1: Apply template transformation
    const transformed = dtl.apply(cleaned, dtlTemplate);

    // Step 2: Parse HL7 date fields (e.g., convert "20250418045253" → Date)
    const parsedResult = applyHL7DateParsing(transformed);

    let resolvedResult = parsedResult;
    if (Object.keys(groupMap).length > 0) // Step 3: Resolve master data (e.g., race, language, etc. via groupMap)
      resolvedResult = await resolveMasterDataFields(parsedResult, groupMap);

    // Step 4: Clean final result — remove undefined fields using a loop
    const finalPayload = cleanDTLResult(resolvedResult, typeOfFunction);

    if (typeOfFunction == 'parseData') return finalPayload;

    return {
      transformedData: finalPayload,
      eventCodeDetails: getMapping_EventCodeDetails(cleaned, typeOfFunction)
    }
  } catch (error) {
    throw new Error(`DTL transformation failed: ${error.message}`);
  }
}

function getMasterDataGroupMap(models) {
  const groupMap = {};

  for (const [modelName, model] of Object.entries(models)) {
    for (const [field, attr] of (model?.rawAttributes ? Object.entries(model.rawAttributes) : [])) {
      const assoc = Object.values(model.associations).find(
        (assoc) => assoc.foreignKey === field && assoc.scope?.group
      );
      if (assoc) {
        groupMap[modelName] = groupMap[modelName] || {};
        groupMap[modelName][field] = assoc.scope.group; // Attach group name
      }
    }
  }

  return groupMap;
}


function getMapping_EventCodeDetails(validated, typeOfFunction) {

  const msgType = (validated["MSH.9.1"] || "").toUpperCase();
  const eventCode = (validated["MSH.9.2"] || "").toUpperCase();

  let mapping;
  if (typeOfFunction === 'hl7-processor') {
    // Pull out both the message‐type (ADT vs SIU etc) and the event code
    if (msgType === "ADT") {
      if (!ADT_TYPE_STATUS_MAP.hasOwnProperty(eventCode))
        throw new Error(`Unsupported HL7 event code: ${eventCode}`);
      mapping = ADT_TYPE_STATUS_MAP[eventCode];
    } else {
      if (!SIU_TYPE_STATUS_MAP.hasOwnProperty(eventCode))
        throw new Error(`Unsupported HL7 event code: ${eventCode}`);
      mapping = SIU_TYPE_STATUS_MAP?.[eventCode];
    }
  }

  let returnObj = { message_type: msgType, eventCode };
  if (mapping)
    returnObj = { ...returnObj, ...mapping };

  return returnObj;
}

module.exports = {
  parseHL7Date,
  isDateType,
  buildSpec,
  buildPayload,
  parseHL7Message,
  transformWithDTL,
  getMasterDataGroupMap,
};
