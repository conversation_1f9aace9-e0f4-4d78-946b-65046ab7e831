'use strict';

module.exports = {
    up: async (queryInterface, Sequelize) => {
        return queryInterface.sequelize.query(` 
            CREATE VIEW view_visit_guest AS
            SELECT g.guest_id,
                g.email AS guest_email,
                g.mobile_phone as guest_mobile_phone,
                concat(g.first_name, ' ', g.last_name) AS guest_name,
                g.image as guest_image,
                f.facility_id,
                v.visit_id,
                v.start_date,
                v.start_time,
                v.host_id,
                v.created_at AS visit_created_at,
                f.name AS facility_name,
                concat(i.first_name, ' ', i.last_name) AS host_name,
                i.eid as host_eid,
                i.job_title as host_job_title,
                i.status as host_status,
                i.start_date as host_start_date,
                i.end_date as host_end_date,
                i.email as host_email,
                i.mobile as host_phone,
                i.eid AS emp_id,
                gv.check_in_time,
                gv.check_out_time,
                gv.guest_status
                
            FROM guest_visit gv
                JOIN visit v ON gv.visit_id = v.visit_id
                JOIN guest g ON gv.guest_id = g.guest_id
                JOIN identity i ON v.host_id = i.identity_id
                JOIN facility f ON v.facility_id = f.facility_id
      `);
    },

    down: async (queryInterface, Sequelize) => {
        return queryInterface.sequelize.query(`
      DROP VIEW IF EXISTS view_visit_guest;
    `);
    },
};
