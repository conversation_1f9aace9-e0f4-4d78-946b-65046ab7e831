const { MEDIA } = require("../config/attributes");
const history = require("./plugins/history.plugin");
const media = require("./plugins/media.plugin");

module.exports = (sequelize, DataTypes) => {
  const AppointmentGuestNdaSignature = sequelize.define(
    "AppointmentGuestNdaSignature",
    {
      appointment_guest_nda_signature_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      appointment_guest_nda_agreement_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "appointment_guest_nda_agreement",
          key: "appointment_guest_nda_agreement_id",
        },
        onDelete: "CASCADE",
      },
      signature_method: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      signature: {
        type: MEDIA,
        allowNull: true,
        allowMultiple: false,
      },
    },
    {
      tableName: "appointment_guest_nda_signature",
      timestamps: true,
      underscored: true,
    }
  );

  AppointmentGuestNdaSignature.associate = (models) => {
    AppointmentGuestNdaSignature.belongsTo(models.AppointmentGuestNdaAgreement, {
      foreignKey: "appointment_guest_nda_agreement_id",
      as: "agreement",
    });

    AppointmentGuestNdaSignature.belongsTo(models.MasterData, {
      foreignKey: "signature_method",
      targetKey: "key",
      as: "appointment_guest_nda_signature_method_name",
      constraints: false,
      scope: {
        group: "patient_nda_signature_method",
      },
    });
  };

  history(AppointmentGuestNdaSignature, sequelize, DataTypes);
  media(AppointmentGuestNdaSignature, sequelize, DataTypes);

  return AppointmentGuestNdaSignature;
};
