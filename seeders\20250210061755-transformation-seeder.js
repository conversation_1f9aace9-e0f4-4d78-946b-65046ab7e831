const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transformations = [
      {
        transformation_id: uuidv4(),
        queue_name: "hl7_queue",
        function_name: "getHL7Data",
        display_name: "HL7 Processor Transformation",
        description: "Transforms HL7 messages for processing",
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        transformation_id: uuidv4(),
        queue_name: "hl7_store",
        function_name: "getHL7StoreData",
        display_name: "HL7 Store Transformation",
        description: "Transforms HL7 messages for storage",
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        transformation_id: uuidv4(),
        queue_name: "hr_csv_data",
        function_name: "getHRData",
        display_name: "HR CSV Data Transformation",
        description: "Transforms HR CSV data for processing",
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];

    await queryInterface.bulkInsert("transformations", transformations);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("transformations", null, {});
  },
};