"use strict";
const { v4: uuidv4 } = require("uuid");
const { faker } = require("@faker-js/faker");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Fetch references
      const facilities = await queryInterface.sequelize.query(
        "SELECT facility_id FROM facility LIMIT 5;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );
      const cards = await queryInterface.sequelize.query(
        "SELECT card_id FROM card LIMIT 5;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );
      const systems = await queryInterface.sequelize.query(
        "SELECT system_id FROM system LIMIT 5;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      const accessLevels = Array.from({ length: 50 }).map((_, i) => ({
        access_level_id: uuidv4(),
        name: `Access Level ${faker.word.noun()} ${i + 1}`,
        pacs_area_name: faker.location.city(),
        description: faker.lorem.sentence(),
        pacs_access_level_id: faker.string.alphanumeric(8),
        system_id: systems.length > 0 ? faker.helpers.arrayElement(systems).system_id : null,
        card_id: cards.length > 0 ? faker.helpers.arrayElement(cards).card_id : null,
        status: faker.helpers.arrayElement([0, 1]),
        access_level_type: faker.helpers.arrayElement([0, 1, 2, 3, 4, 5, 6, 7]),
        facility_id: facilities.length > 0 ? faker.helpers.arrayElement(facilities).facility_id : null,
        online: faker.datatype.boolean(),
        requestable_self_service: faker.datatype.boolean(),
        updated_by: null,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("access_level", accessLevels, { transaction });

      // Only create facility_access_level records if there are facilities available
      if (facilities.length > 0) {
        // Filter access levels that have a facility_id and create facility_access_level records
        const facilityAccessLevels = accessLevels
          .filter(accessLevel => accessLevel.facility_id !== null)
          .map((accessLevel) => ({
            facility_access_level_id: uuidv4(),
            access_level_id: accessLevel.access_level_id,
            facility_id: accessLevel.facility_id,
            default_access_guest: faker.datatype.boolean(),
            requestable_guest: faker.datatype.boolean(),
            created_at: new Date(),
            updated_at: new Date(),
          }));

        if (facilityAccessLevels.length > 0) {
          await queryInterface.bulkInsert("facility_access_level", facilityAccessLevels, { transaction });
        }
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Delete facility_access_level records first due to foreign key constraints
    await queryInterface.bulkDelete("facility_access_level", null, {});
    await queryInterface.bulkDelete("access_level", null, {});
  },
};
