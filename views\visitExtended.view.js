module.exports = (sequelize, DataTypes) => {
  const VisitExtendedView = sequelize.define(
    "VisitExtendedView",
    {
      visit_id: {
        type: DataTypes.UUID,
        primaryKey: true,
      },
      title: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      type: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      category: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      start_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      start_time: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      duration: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      end_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      facility_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      host_id: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      facility_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      host_first_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      host_last_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      host_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      host_email: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      host_phone: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      extra_data: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
    },
    {
      tableName: "view_visit_extended",
      timestamps: false,
      underscored: true,
    }
  );

  return VisitExtendedView;
};