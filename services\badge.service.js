const { createCanvas, loadImage } = require("canvas");
const JsBarcode = require("jsbarcode");
const QRCode = require("qrcode");

/**
 * Generate badge image from template content and mapped data
 * @param {Object} templateContent - Badge template content with canvas config and elements
 * @param {Object} mappedData - Mapped instance data for dynamic content
 * @param {string} format - Output format (PNG, JPEG, JPG, WEBP, BMP, TIFF, PDF)
 * @returns {<PERSON>uffer} Generated image buffer
 */
async function generateBadgeImage(templateContent, mappedData, format) {
  const { canvasConfig, elements } = templateContent;
  
  // Create canvas with template dimensions
  const canvas = createCanvas(canvasConfig.width, canvasConfig.height);
  const ctx = canvas.getContext('2d');

  // Set background
  ctx.fillStyle = canvasConfig.background || '#ffffff';
  ctx.fillRect(0, 0, canvasConfig.width, canvasConfig.height);

  // Sort elements by zIndex to render in correct order
  const sortedElements = elements.sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0));

  // Render each element
  for (const element of sortedElements) {
    if (!element.visible) continue;

    ctx.save();
    
    // Apply transformations
    if (element.rotation) {
      const centerX = element.x + element.width / 2;
      const centerY = element.y + element.height / 2;
      ctx.translate(centerX, centerY);
      ctx.rotate((element.rotation * Math.PI) / 180);
      ctx.translate(-centerX, -centerY);
    }

    try {
      switch (element.type) {
        case 'text':
          await renderTextElement(ctx, element, mappedData);
          break;
        case 'image':
          await renderImageElement(ctx, element, mappedData);
          break;
        case 'barcode':
          await renderBarcodeElement(ctx, element, mappedData);
          break;
        case 'qrcode':
          await renderQRCodeElement(ctx, element, mappedData);
          break;
        case 'shape':
          await renderShapeElement(ctx, element);
          break;
        default:
          console.warn(`Unknown element type: ${element.type}`);
      }
    } catch (error) {
      console.error(`Error rendering element ${element.id}:`, error);
    }

    ctx.restore();
  }

  // Convert to buffer based on format
  const outputFormat = format.toLowerCase();
  switch (outputFormat) {
    case 'png':
      return canvas.toBuffer('image/png');
    case 'jpeg':
    case 'jpg':
      return canvas.toBuffer('image/jpeg', { quality: 0.9 });
    case 'webp':
      return canvas.toBuffer('image/webp', { quality: 0.9 });
    case 'bmp':
      return canvas.toBuffer('image/bmp');
    case 'tiff':
      return canvas.toBuffer('image/tiff');
    default:
      // Default to PNG for unsupported formats including PDF
      return canvas.toBuffer('image/png');
  }
}

/**
 * Render text element on canvas
 */
async function renderTextElement(ctx, element, mappedData) {
  let text = element.text || '';
  
  // Replace dynamic placeholders with actual data
  text = replacePlaceholders(text, mappedData);
  
  // Set font properties
  let fontStyle = '';
  if (element.fontStyle) {
    if (element.fontStyle.includes('bold')) fontStyle += 'bold ';
    if (element.fontStyle.includes('italic')) fontStyle += 'italic ';
  }
  
  ctx.font = `${fontStyle}${element.fontSize || 16}px ${element.fontFamily || 'Arial'}`;
  ctx.fillStyle = element.color || '#000000';
  ctx.textAlign = element.textAlign || 'left';
  
  // Handle text alignment and positioning
  let x = element.x;
  if (element.textAlign === 'center') {
    x = element.x + element.width / 2;
  } else if (element.textAlign === 'right') {
    x = element.x + element.width;
  }
  
  // Handle multi-line text if needed
  if (element.autoResize || element.width) {
    const lines = wrapText(ctx, text, element.width);
    const lineHeight = (element.fontSize || 16) * 1.2;
    
    lines.forEach((line, index) => {
      ctx.fillText(line, x, element.y + (element.fontSize || 16) + (index * lineHeight));
    });
  } else {
    ctx.fillText(text, x, element.y + (element.fontSize || 16));
  }
}

/**
 * Render image element on canvas
 */
async function renderImageElement(ctx, element, mappedData) {
  let imageSrc = element.src;
  
  // Handle dynamic images
  if (element.isDynamic && element.dynamicSrc) {
    imageSrc = replacePlaceholders(element.dynamicSrc, mappedData);
  }
  
  if (!imageSrc) return;
  
  try {
    let image;
    
    // Handle base64 images
    if (imageSrc.startsWith('data:')) {
      image = await loadImage(imageSrc);
    } else if (imageSrc.startsWith('http')) {
      // Handle URL images
      image = await loadImage(imageSrc);
    } else {
      // Handle file path images
      const path = require('path');
      const fs = require('fs');
      const imagePath = path.join(__dirname, '../uploads', imageSrc);
      if (fs.existsSync(imagePath)) {
        image = await loadImage(imagePath);
      } else {
        console.warn(`Image not found: ${imagePath}`);
        return;
      }
    }
    
    // Apply border radius if specified
    if (element.borderRadius) {
      ctx.save();
      ctx.beginPath();
      ctx.roundRect(element.x, element.y, element.width, element.height, element.borderRadius);
      ctx.clip();
    }
    
    // Draw image with aspect ratio handling
    if (element.maintainAspectRatio) {
      const aspectRatio = image.width / image.height;
      const elementAspectRatio = element.width / element.height;
      
      let drawWidth = element.width;
      let drawHeight = element.height;
      let drawX = element.x;
      let drawY = element.y;
      
      if (aspectRatio > elementAspectRatio) {
        drawHeight = element.width / aspectRatio;
        drawY = element.y + (element.height - drawHeight) / 2;
      } else {
        drawWidth = element.height * aspectRatio;
        drawX = element.x + (element.width - drawWidth) / 2;
      }
      
      ctx.drawImage(image, drawX, drawY, drawWidth, drawHeight);
    } else {
      ctx.drawImage(image, element.x, element.y, element.width, element.height);
    }
    
    if (element.borderRadius) {
      ctx.restore();
    }
  } catch (error) {
    console.error('Error loading image:', error);
    // Draw placeholder rectangle
    ctx.strokeStyle = '#cccccc';
    ctx.strokeRect(element.x, element.y, element.width, element.height);
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(element.x, element.y, element.width, element.height);
  }
}

/**
 * Render barcode element on canvas
 */
async function renderBarcodeElement(ctx, element, mappedData) {
  let barcodeData = element.data || '';
  
  // Replace dynamic placeholders with actual data
  barcodeData = replacePlaceholders(barcodeData, mappedData);
  
  if (!barcodeData) return;
  
  try {
    // Create a temporary canvas for barcode generation
    const barcodeCanvas = createCanvas(element.width, element.height);
    
    // Generate barcode using JsBarcode
    JsBarcode(barcodeCanvas, barcodeData, {
      format: element.format || 'CODE128',
      width: 2,
      height: element.height * 0.8,
      displayValue: false,
      background: 'transparent',
      lineColor: '#000000'
    });
    
    // Draw the barcode canvas onto main canvas
    ctx.drawImage(barcodeCanvas, element.x, element.y);
  } catch (error) {
    console.error('Error generating barcode:', error);
    // Draw placeholder rectangle
    ctx.strokeStyle = '#cccccc';
    ctx.strokeRect(element.x, element.y, element.width, element.height);
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(element.x, element.y, element.width, element.height);
  }
}

/**
 * Render QR code element on canvas
 */
async function renderQRCodeElement(ctx, element, mappedData) {
  let qrData = element.data || '';
  
  // Replace dynamic placeholders with actual data
  qrData = replacePlaceholders(qrData, mappedData);
  
  if (!qrData) return;
  
  try {
    // Generate QR code as data URL
    const qrDataURL = await QRCode.toDataURL(qrData, {
      width: Math.min(element.width, element.height),
      margin: 1,
      color: {
        dark: element.color || '#000000',
        light: element.backgroundColor || '#ffffff'
      }
    });
    
    // Load and draw QR code image
    const qrImage = await loadImage(qrDataURL);
    ctx.drawImage(qrImage, element.x, element.y, element.width, element.height);
  } catch (error) {
    console.error('Error generating QR code:', error);
    // Draw placeholder rectangle
    ctx.strokeStyle = '#cccccc';
    ctx.strokeRect(element.x, element.y, element.width, element.height);
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(element.x, element.y, element.width, element.height);
  }
}

/**
 * Render shape element on canvas
 */
async function renderShapeElement(ctx, element) {
  ctx.fillStyle = element.fill || '#ffffff';
  ctx.strokeStyle = element.stroke || '#000000';
  ctx.lineWidth = element.strokeWidth || 1;

  const centerX = element.x + element.width / 2;
  const centerY = element.y + element.height / 2;

  switch (element.shapeType) {
    case 'rectangle':
      ctx.fillRect(element.x, element.y, element.width, element.height);
      if (element.stroke) {
        ctx.strokeRect(element.x, element.y, element.width, element.height);
      }
      break;

    case 'circle':
      const radius = Math.min(element.width, element.height) / 2;
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      ctx.fill();
      if (element.stroke) {
        ctx.stroke();
      }
      break;

    case 'star':
      drawStar(ctx, centerX, centerY, element.numPoints || 5,
               Math.min(element.width, element.height) / 2,
               Math.min(element.width, element.height) / 4);
      ctx.fill();
      if (element.stroke) {
        ctx.stroke();
      }
      break;

    default:
      // Default to rectangle
      ctx.fillRect(element.x, element.y, element.width, element.height);
      if (element.stroke) {
        ctx.strokeRect(element.x, element.y, element.width, element.height);
      }
  }
}

/**
 * Replace placeholders in text with actual data
 * Supports dot notation for nested objects and basic formatting
 */
function replacePlaceholders(text, mappedData) {
  if (!text || typeof text !== 'string') return text;

  // Enhanced regex to support dot notation and more complex keys
  return text.replace(/\{\{([^}]+)\}\}/g, (match, key) => {
    const trimmedKey = key.trim();

    // Handle dot notation for nested objects
    const value = getNestedValue(mappedData, trimmedKey);

    // Return the value if found, otherwise return the original placeholder
    if (value !== undefined && value !== null) {
      return formatValue(value);
    }

    // Log missing variables for debugging
    console.warn(`Badge template variable not found: ${trimmedKey}`);
    return match; // Return original placeholder if value not found
  });
}

/**
 * Get nested value from object using dot notation
 * @param {Object} obj - The object to search in
 * @param {string} path - The dot notation path (e.g., 'user.profile.name')
 * @returns {*} The value at the path or undefined
 */
function getNestedValue(obj, path) {
  if (!obj || typeof obj !== 'object') return undefined;

  // Split the path and traverse the object
  const keys = path.split('.');
  let current = obj;

  for (const key of keys) {
    if (current === null || current === undefined) return undefined;
    if (typeof current !== 'object') return undefined;
    current = current[key];
  }

  return current;
}

/**
 * Format value for display in badge
 * @param {*} value - The value to format
 * @returns {string} Formatted value
 */
function formatValue(value) {
  if (value === null || value === undefined) return '';

  // Handle dates
  if (value instanceof Date) {
    return value.toLocaleDateString();
  }

  // Handle objects (convert to JSON string)
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value);
    } catch (error) {
      return '[Object]';
    }
  }

  // Handle booleans
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }

  // Convert to string
  return String(value);
}

/**
 * Wrap text to fit within specified width
 */
function wrapText(ctx, text, maxWidth) {
  if (!maxWidth) return [text];

  const words = text.split(' ');
  const lines = [];
  let currentLine = words[0];

  for (let i = 1; i < words.length; i++) {
    const word = words[i];
    const width = ctx.measureText(currentLine + ' ' + word).width;

    if (width < maxWidth) {
      currentLine += ' ' + word;
    } else {
      lines.push(currentLine);
      currentLine = word;
    }
  }

  lines.push(currentLine);
  return lines;
}

/**
 * Draw a star shape
 */
function drawStar(ctx, cx, cy, spikes, outerRadius, innerRadius) {
  let rot = Math.PI / 2 * 3;
  let x = cx;
  let y = cy;
  const step = Math.PI / spikes;

  ctx.beginPath();
  ctx.moveTo(cx, cy - outerRadius);

  for (let i = 0; i < spikes; i++) {
    x = cx + Math.cos(rot) * outerRadius;
    y = cy + Math.sin(rot) * outerRadius;
    ctx.lineTo(x, y);
    rot += step;

    x = cx + Math.cos(rot) * innerRadius;
    y = cy + Math.sin(rot) * innerRadius;
    ctx.lineTo(x, y);
    rot += step;
  }

  ctx.lineTo(cx, cy - outerRadius);
  ctx.closePath();
}

module.exports = {
  generateBadgeImage
};
