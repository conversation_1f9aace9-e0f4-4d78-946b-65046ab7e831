"use strict";
const { v4: uuidv4 } = require("uuid");
const { faker } = require("@faker-js/faker");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Get existing facilities
      const facilities = await queryInterface.sequelize.query(
        "SELECT facility_id FROM facility LIMIT 3;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (facilities.length === 0) {
        throw new Error("No facilities found. Please run facility seeder first.");
      }

      // Create identities (hosts)
      const identities = Array.from({ length: 10 }).map(() => ({
        identity_id: uuidv4(),
        first_name: faker.person.firstName(),
        last_name: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.phone.number().slice(0, 20),
        status: 0, // Active
        identity_type: 0, // COS
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("identity", identities, { transaction });

      // Create guests
      const guests = Array.from({ length: 15 }).map(() => ({
        guest_id: uuidv4(),
        first_name: faker.person.firstName(),
        last_name: faker.person.lastName(),
        email: faker.internet.email(),
        mobile_phone: faker.phone.number().slice(0, 20),
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("guest", guests, { transaction });

      // Create visits
      const visits = Array.from({ length: 20 }).map(() => ({
        visit_id: uuidv4(),
        host_id: faker.helpers.arrayElement(identities).identity_id,
        facility_id: faker.helpers.arrayElement(facilities).facility_id,
        start_date: faker.date.between({ 
          from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 
          to: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) 
        }),
        start_time: faker.date.recent().toTimeString().slice(0, 8), // HH:MM:SS format
        duration: faker.number.int({ min: 30, max: 240 }).toString(),
        status: faker.helpers.arrayElement([0, 1, 2, 3]),
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("visit", visits, { transaction });

      // Create guest_visit relationships
      const guestVisits = [];
      visits.forEach(visit => {
        // Each visit can have 1-3 guests
        const numGuests = faker.number.int({ min: 1, max: 3 });
        const selectedGuests = faker.helpers.arrayElements(guests, numGuests);
        
        selectedGuests.forEach(guest => {
          const checkInTime = faker.date.recent({ days: 7 });
          const checkOutTime = faker.datatype.boolean() ? 
            faker.date.between({ from: checkInTime, to: new Date() }) : null;

          guestVisits.push({
            guest_visit_id: uuidv4(),
            guest_id: guest.guest_id,
            visit_id: visit.visit_id,
            check_in_time: checkInTime,
            check_out_time: checkOutTime,
            guest_status: faker.helpers.arrayElement([0, 1, 2, 3]), // Invited, Checked In, Checked Out, Check In Denied
            created_at: new Date(),
            updated_at: new Date(),
          });
        });
      });

      await queryInterface.bulkInsert("guest_visit", guestVisits, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.bulkDelete("guest_visit", null, { transaction });
      await queryInterface.bulkDelete("visit", null, { transaction });
      await queryInterface.bulkDelete("guest", null, { transaction });
      await queryInterface.bulkDelete("identity", null, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};




