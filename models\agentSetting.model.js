module.exports = (sequelize, DataTypes) => {
  const AgentSetting = sequelize.define(
    'AgentSetting',
    {
      agent_setting_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      agent_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'agent',
          key: 'agent_id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      key: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      value: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      key_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      description: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      is_encrypted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: 'agent_setting',
      timestamps: true,
      underscored: true,
    }
  );

  AgentSetting.associate = function(models) {
    AgentSetting.belongsTo(models.Agent, {
      foreignKey: 'agent_id',
      as: 'agent'
    });
  };

  return AgentSetting;
};
