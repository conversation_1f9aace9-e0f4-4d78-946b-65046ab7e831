const express = require("express");
const auth = require("../middlewares/auth");
const uploadToBase64 = require("../middlewares/upload");
const validate = require("../middlewares/validate");
const GuestValidation = require("../validations/guest.validation");
const GuestController = require("../controllers/guest.controller");

const router = express.Router();

/**
 * @swagger
 * /guests/visits:
 *   get:
 *     summary: Get all visits with pagination and filtering
 *     tags: [Visits]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order
 *       - in: query
 *         name: facility_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by facility ID
 *       - in: query
 *         name: host_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by host ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [Scheduled, In Progress, Completed, Cancelled]
 *         description: Filter by visit status
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by start date
 *     responses:
 *       200:
 *         description: Visits retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Visits retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Visit'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/visits",
  auth("view_visit"),
  validate(GuestValidation.getVisits),
  GuestController.getVisits
);

/**
 * @swagger
 * components:
 *   schemas:
 *     Guest:
 *       type: object
 *       required:
 *         - first_name
 *         - last_name
 *         - email
 *       properties:
 *         guest_id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the guest
 *         first_name:
 *           type: string
 *           maxLength: 100
 *           description: Guest's first name
 *         last_name:
 *           type: string
 *           maxLength: 100
 *           description: Guest's last name
 *         email:
 *           type: string
 *           format: email
 *           maxLength: 255
 *           description: Guest's email address
 *         mobile_phone:
 *           type: string
 *           maxLength: 20
 *           description: Guest's mobile phone number
 *         company:
 *           type: string
 *           maxLength: 200
 *           description: Guest's company name
 *         private_visitor:
 *           type: boolean
 *           default: false
 *           description: Whether the guest is a private visitor
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /guests:
 *   post:
 *     summary: Create a new guest
 *     tags: [Guests]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - first_name
 *               - last_name
 *               - email
 *             properties:
 *               first_name:
 *                 type: string
 *                 maxLength: 100
 *                 example: "John"
 *               last_name:
 *                 type: string
 *                 maxLength: 100
 *                 example: "Doe"
 *               email:
 *                 type: string
 *                 format: email
 *                 maxLength: 255
 *                 example: "<EMAIL>"
 *               mobile_phone:
 *                 type: string
 *                 maxLength: 20
 *                 example: "+1234567890"
 *               company:
 *                 type: string
 *                 maxLength: 200
 *                 example: "Acme Corporation"
 *               private_visitor:
 *                 type: boolean
 *                 default: false
 *                 example: false
 *     responses:
 *       201:
 *         description: Guest created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Guest created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Guest'
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       409:
 *         description: Email already exists
 */
router.post(
  "/",
  auth("create_guest"),
  validate(GuestValidation.createGuest),
  GuestController.createGuest
);

/**
 * @swagger
 * /guests:
 *   get:
 *     summary: Get all guests with pagination and filtering
 *     tags: [Guests]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for name, email, company, or phone
 *       - in: query
 *         name: company
 *         schema:
 *           type: string
 *         description: Filter by company name
 *       - in: query
 *         name: private_visitor
 *         schema:
 *           type: boolean
 *         description: Filter by private visitor status
 *     responses:
 *       200:
 *         description: Guests retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Guests retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Guest'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/",
  auth("view_guest"),
  validate(GuestValidation.getGuests),
  GuestController.getGuests
);

/**
 * @swagger
 * /guests/{id}:
 *   get:
 *     summary: Get a guest by ID
 *     tags: [Guests]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: guest_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Guest ID
 *     responses:
 *       200:
 *         description: Guest retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Guest retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Guest'
 *       404:
 *         description: Guest not found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/:guest_id",
  auth("view_guest"),
  validate(GuestValidation.getGuestById),
  GuestController.getGuestById
);

/**
 * @swagger
 * /guests/{id}:
 *   put:
 *     summary: Update a guest by ID
 *     tags: [Guests]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: guest_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Guest ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *                 maxLength: 100
 *                 example: "John"
 *               last_name:
 *                 type: string
 *                 maxLength: 100
 *                 example: "Doe"
 *               email:
 *                 type: string
 *                 format: email
 *                 maxLength: 255
 *                 example: "<EMAIL>"
 *               mobile_phone:
 *                 type: string
 *                 maxLength: 20
 *                 example: "+1234567890"
 *               company:
 *                 type: string
 *                 maxLength: 200
 *                 example: "Acme Corporation"
 *               private_visitor:
 *                 type: boolean
 *                 example: false
 *     responses:
 *       200:
 *         description: Guest updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Guest updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Guest'
 *       404:
 *         description: Guest not found
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.put(
  "/:guest_id",
  auth("edit_guest"),
  validate(GuestValidation.updateGuest),
  GuestController.updateGuest
);



/**
 * @swagger
 * /guests/{guest_id}:
 *   delete:
 *     summary: Delete a guest by ID
 *     tags: [Guests]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: guest_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Guest ID
 *     responses:
 *       200:
 *         description: Guest deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Guest deleted successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     guest_id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *       404:
 *         description: Guest not found
 *       401:
 *         description: Unauthorized
 */
router.delete(
  "/:guest_id",
  auth("delete_guest"),
  validate(GuestValidation.deleteGuest),
  GuestController.deleteGuest
);

// ==================== VISIT ROUTES ====================

/**
 * @swagger
 * components:
 *   schemas:
 *     Visit:
 *       type: object
 *       required:
 *         - facility_id
 *         - host_id
 *         - start_date
 *         - start_time
 *         - duration
 *       properties:
 *         visit_id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the visit
 *         facility_id:
 *           type: string
 *           format: uuid
 *           description: Facility where visit takes place
 *         host_id:
 *           type: string
 *           format: uuid
 *           description: Host identity ID
 *         escort_id:
 *           type: string
 *           format: uuid
 *           description: Escort identity ID (optional)
 *         start_date:
 *           type: string
 *           format: date
 *           description: Visit start date
 *         start_time:
 *           type: string
 *           pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$'
 *           description: Visit start time (HH:MM:SS)
 *         duration:
 *           type: integer
 *           minimum: 1
 *           description: Visit duration in minutes
 */

/**
 * @swagger
 * /guests/visits:
 *   post:
 *     summary: Create a new visit
 *     tags: [Visits]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - facility_id
 *               - host_id
 *               - start_date
 *               - start_time
 *               - duration
 *             properties:
 *               facility_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               host_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               escort_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               start_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-02-01"
 *               start_time:
 *                 type: string
 *                 pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$'
 *                 example: "14:30:00"
 *               duration:
 *                 type: integer
 *                 minimum: 1
 *                 example: 120
 *               purpose:
 *                 type: string
 *                 maxLength: 500
 *                 example: "Business meeting"
 *               status:
 *                 type: string
 *                 enum: [Scheduled, In Progress, Completed, Cancelled]
 *                 default: Scheduled
 *                 example: "Scheduled"
 *     responses:
 *       201:
 *         description: Visit created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Visit created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Visit'
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/visits",
  auth("create_visit"),
  validate(GuestValidation.createVisit),
  GuestController.createVisit
);



/**
 * @swagger
 * /guests/visits/{visit_id}:
 *   get:
 *     summary: Get a visit by ID
 *     tags: [Visits]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID
 *     responses:
 *       200:
 *         description: Visit retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Visit retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Visit'
 *       404:
 *         description: Visit not found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/visits/:visit_id",
  auth("view_visit"),
  validate(GuestValidation.getVisitById),
  GuestController.getVisitById
);

/**
 * @swagger
 * /guests/visits/{visit_id}:
 *   put:
 *     summary: Update a visit by ID
 *     tags: [Visits]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               facility_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               host_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               escort_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               start_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-02-01"
 *               start_time:
 *                 type: string
 *                 pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$'
 *                 example: "14:30:00"
 *     responses:
 *       200:
 *         description: Visit updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Visit updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Visit'
 *       404:
 *         description: Visit not found
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.put(
  "/visits/:visit_id",
  auth("edit_visit"),
  validate(GuestValidation.updateVisit),
  GuestController.updateVisit
);

/**
 * @swagger
 * /guests/visits/{visit_id}:
 *   delete:
 *     summary: Delete a visit by ID
 *     tags: [Visits]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID
 *     responses:
 *       200:
 *         description: Visit deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Visit deleted successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     visit_id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *       404:
 *         description: Visit not found
 *       401:
 *         description: Unauthorized
 */
router.delete(
  "/visits/:visit_id",
  auth("delete_visit"),
  validate(GuestValidation.deleteVisit),
  GuestController.deleteVisit
);

/**
 * @swagger
 * /guests/{guest_id}/image:
 *   patch:
 *     summary: Update guest image
 *     tags: [Guests]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: guest_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Guest ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Image file to upload
 *     responses:
 *       200:
 *         description: Guest image updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Guest image updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Guest'
 *       400:
 *         description: Invalid input data
 *       404:
 *         description: Guest not found
 *       401:
 *         description: Unauthorized
 */
router.patch(
  "/:guest_id/image",
  auth("edit_guest"),
  uploadToBase64("image"),
  validate(GuestValidation.updateImage),
  GuestController.updateImage
);

module.exports = router;
