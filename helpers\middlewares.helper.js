const { transformWithDTL } = require(`${process.cwd()}/helpers/hl7.helper`);;
const hrMappingConfig = require(`${process.cwd()}/mappings/hrData.config.json`);
const hrColumnNameDTL = require(`${process.cwd()}/mappings/hrData.dtl.json`);
const hrColumnIndexDTL = require(`${process.cwd()}/mappings/hrData.columnIndex.dtl.json`);

module.exports = {
    transformHRDataWithDTL: async (data, are_headers_present) => {
        const dtlTemplate = are_headers_present ? hrColumnNameDTL : hrColumnIndexDTL;
        const transformed = await transformWithDTL(data, dtlTemplate, {}, 'parseData');

        // Convert empty strings to null in the transformed data
        const convertEmptyToNull = (obj) => {
            for (const key in obj) {
                if (obj[key] === "") {
                    obj[key] = null;
                } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                    convertEmptyToNull(obj[key]);
                }
            }
            return obj;
        };

        return convertEmptyToNull(transformed);
    },
    validateTransformedData: (transformedData) => {
        const errors = [];

        // Get the model data (assuming Identity model)
        const modelData = transformedData.Identity;
        if (!modelData) {
            errors.push('No Identity data found in transformed data');
            return { isValid: false, errors };
        }

        // Check required fields: ["email", "firstName", "lastName"]
        if (hrMappingConfig.required) {
            for (const requiredField of hrMappingConfig.required) {
                // Direct field mapping based on DTL template structure
                let targetField;
                switch (requiredField) {
                    case 'email':
                        targetField = 'email';
                        break;
                    case 'firstName':
                        targetField = 'first_name';
                        break;
                    case 'lastName':
                        targetField = 'last_name';
                        break;
                    default:
                        continue;
                }

                if (!modelData[targetField]) {
                    errors.push(`${requiredField} is required`);
                }
            }
        }

        // Check validation rules for: email, firstName, lastName, identityType, status
        if (hrMappingConfig.validation) {
            for (const [sourceField, rules] of Object.entries(hrMappingConfig.validation)) {
                // Direct field mapping based on DTL template structure
                let targetField;
                switch (sourceField) {
                    case 'email':
                        targetField = 'email';
                        break;
                    case 'firstName':
                        targetField = 'first_name';
                        break;
                    case 'lastName':
                        targetField = 'last_name';
                        break;
                    case 'identityType':
                        targetField = 'identity_type';
                        break;
                    case 'status':
                        targetField = 'status';
                        break;
                    default:
                        continue;
                }

                const value = modelData[targetField];

                // Skip validation if field is empty (unless required, which was checked above)
                if (!value) continue;

                // Email validation
                if (rules.type === 'email') {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        errors.push(`${sourceField} must be a valid email address`);
                    }
                }

                // Number validation
                if (rules.type === 'number') {
                    if (isNaN(Number(value))) {
                        errors.push(`${sourceField} must be a number`);
                    }
                }

                // String validation
                if (rules.type === 'string' && typeof value !== 'string') {
                    errors.push(`${sourceField} must be a string`);
                }
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
