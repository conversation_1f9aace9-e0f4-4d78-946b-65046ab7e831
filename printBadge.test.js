const http = require('http');
const assert = require('assert');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3001';
let accessToken;
let badgeId = "ea201806-56a8-4bc3-936e-45ca029c0b9c";
let instance_id = "101c913b-7d94-4b3a-a0ae-19967aa272e7"; // Valid <PERSON>ientAppointmentGuestView ID

// Helper function to make HTTP requests
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      const jsonData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(jsonData);
    }

    const req = http.request(options, (res) => {
      const chunks = [];

      res.on('data', (chunk) => {
        chunks.push(chunk);
      });

      res.on('end', () => {
        const buffer = Buffer.concat(chunks);

        // Check if response is JSON based on content-type
        const contentType = res.headers['content-type'] || '';

        if (contentType.includes('application/json')) {
          try {
            const parsedBody = JSON.parse(buffer.toString());
            resolve({
              status: res.statusCode,
              headers: res.headers,
              body: parsedBody,
              rawBody: buffer.toString(),
              buffer: buffer
            });
          } catch (error) {
            resolve({
              status: res.statusCode,
              headers: res.headers,
              body: buffer.toString(),
              rawBody: buffer.toString(),
              buffer: buffer
            });
          }
        } else {
          // For binary data (images, etc.)
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: buffer,
            rawBody: buffer.toString(),
            buffer: buffer
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testBadgePrintAPI() {
  console.log('🚀 Starting badge print API test...\n');
  console.log(`📡 Testing against: ${BASE_URL}\n`);

  try {
    // Test server connectivity first
    console.log('🔍 Checking server connectivity...');
    try {
      await makeRequest('GET', '/');
      console.log('✅ Server is reachable\n');
    } catch (error) {
      console.log('❌ Server connectivity failed. Make sure the server is running on localhost:3001');
      throw new Error(`Server not reachable: ${error.message}`);
    }

    // Login
    console.log('📝 Testing login...');
    const loginResponse = await makeRequest('POST', '/api/auth/login', {
      email: '<EMAIL>',
      password: 'Pa$$w0rd!'
    });

    console.log('Login Response Status:', loginResponse.status);
    console.log('Login Response Body:', loginResponse.body);

    assert.strictEqual(loginResponse.status, 200, 'Login should return status 200');
    assert(loginResponse.body.data && loginResponse.body.data.tokens && loginResponse.body.data.tokens.access && loginResponse.body.data.tokens.access.token,
           'Login response should contain access token');

    accessToken = loginResponse.body.data.tokens.access.token;
    console.log('✅ Login successful');
    console.log('🔑 Access token obtained\n');

    // Test badge printing
    console.log('🖨️  Testing badge printing...');
    const printData = {
      badge_id: badgeId,
      instance_id: instance_id
    };

    const printResponse = await makeRequest('POST', '/api/badges/print', printData, {
      'Authorization': `Bearer ${accessToken}`
    });

    console.log('Print Response Status:', printResponse.status);
    console.log('Print Response Headers:', printResponse.headers);

    if (printResponse.status === 200) {
      console.log('✅ Badge print successful!');
      console.log('Content-Type:', printResponse.headers['content-type']);

      if (Buffer.isBuffer(printResponse.body)) {
        console.log('Image size:', printResponse.body.length, 'bytes');

        // Download and save the image
        const contentDisposition = printResponse.headers['content-disposition'];
        let filename = 'badge_image.jpg';

        // Extract filename from content-disposition header if available
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/);
          if (filenameMatch) {
            filename = filenameMatch[1];
          }
        }

        // Create downloads directory if it doesn't exist
        const downloadsDir = path.join(process.cwd(), 'downloads');
        if (!fs.existsSync(downloadsDir)) {
          fs.mkdirSync(downloadsDir, { recursive: true });
        }

        const filePath = path.join(downloadsDir, filename);

        try {
          fs.writeFileSync(filePath, printResponse.body);
          console.log('💾 Image downloaded successfully!');
          console.log('📁 Saved to:', filePath);
          console.log('📏 File size:', fs.statSync(filePath).size, 'bytes');
        } catch (error) {
          console.log('❌ Failed to save image:', error.message);
        }
      } else {
        console.log('Response body:', printResponse.body);
      }
    } else {
      console.log('❌ Badge print failed');
      console.log('Error response:', printResponse.body);
    }

    assert.strictEqual(printResponse.status, 200, 'Badge print should return status 200');

    console.log('\n🎉 All tests passed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure the server is running on localhost:3001');
    }
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testBadgePrintAPI()
    .then(() => {
      console.log('\n✨ Test execution completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testBadgePrintAPI };
