const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
    const CardShipping = sequelize.define(
        "CardShipping",
        {
            card_shipping_id: {
                type: DataTypes.UUID,
                primaryKey: true,
                defaultValue: DataTypes.UUIDV4,
            },
            card_request_id: {
                type: DataTypes.UUID,
                allowNull: false,
                references: {
                    model: "card_request",
                    key: "card_request_id",
                },
                onDelete: "CASCADE",
            },
            ship_to: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            ship_to_name: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            address_line_1: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            address_line_2: {
                type: DataTypes.STRING,
                allowNull: true,
            },
            country_id: {
                type: DataTypes.UUID,
                allowNull: false,
                references: {
                    model: "country",
                    key: "country_id",
                },
            },
            state_id: {
                type: DataTypes.UUID,
                allowNull: false,
                references: {
                    model: "state",
                    key: "state_id",
                },
            },
            zip_code: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            mobile_phone: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            updated_by: {
                type: DataTypes.UUID,
                allowNull: true,
            },
        },
        {
            tableName: "card_shipping",
            timestamps: true,
            underscored: true,
        }
    );

    CardShipping.associate = (models) => {
        CardShipping.belongsTo(models.CardRequest, {
            foreignKey: "card_request_id",
            as: "card_request",
        });
        CardShipping.belongsTo(models.Country, {
            foreignKey: "country_id",
            as: "country",
        });
        CardShipping.belongsTo(models.State, {
            foreignKey: "state_id",
            as: "state",
        });
    };

    history(CardShipping, sequelize, DataTypes);

    return CardShipping;
};