const express = require("express");
const validate = require("../middlewares/validate");
const { BadgeValidation } = require("../validations");
const { BadgeController } = require("../controllers");
const auth = require("../middlewares/auth");
const uploadToBase64 = require("../middlewares/upload");

const router = express.Router({ mergeParams: true });

/**
 * @swagger
 * tags:
 *   name: Badges
 *   description: Badge management operations
 */

/**
 * @swagger
 * /badges:
 *   get:
 *     summary: Get all badges with pagination and search
 *     tags: [Badges]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, status, created_at, updated_at]
 *           default: updated_at
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for filtering badges
 *     responses:
 *       200:
 *         description: Badges retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Badges retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     results:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           badge_id:
 *                             type: string
 *                             format: uuid
 *                           name:
 *                             type: string
 *                           status:
 *                             type: boolean
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *                         totalResults:
 *                           type: integer
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/",
  auth("view_badges"),
  validate(BadgeValidation.index),
  BadgeController.index
);

/**
 * @swagger
 * /badges/schemas:
 *   get:
 *     summary: Get all available schema names
 *     tags: [Badges]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Schema names retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Schema names retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     schemas:
 *                       type: array
 *                       items:
 *                         type: string
 *                       example: ["patient", "appointment", "facility"]
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/schemas",
  auth("get_all_schemas"),
  BadgeController.getSchemas
);

/**
 * @swagger
 * /badges/model-attributes/{modelName}:
 *   get:
 *     summary: Get model attributes for a specific model
 *     tags: [Badges]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: modelName
 *         required: true
 *         schema:
 *           type: string
 *         description: Model name
 *         example: "patient"
 *     responses:
 *       200:
 *         description: Model attributes retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Model attributes retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     modelName:
 *                       type: string
 *                       example: "Patient"
 *                     attributes:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           type:
 *                             type: string
 *                           allowNull:
 *                             type: boolean
 *                           primaryKey:
 *                             type: boolean
 *       404:
 *         description: Model not found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/model-attributes/:modelName",
  auth("get_all_schema_attribs"),
  validate(BadgeValidation.modelAttributes),
  BadgeController.getModelAttributes
);

/**
 * @swagger
 * /badges/print:
 *   post:
 *     summary: Print badge by generating image from template
 *     tags: [Badges]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - badge_id
 *               - instance_id
 *             properties:
 *               badge_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               instance_id:
 *                 type: string
 *                 example: "patient123"
 *     responses:
 *       200:
 *         description: Badge image generated successfully
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *           image/jpeg:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Validation error
 *       404:
 *         description: Badge or instance not found
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/print",
  auth("print_badge"),
  validate(BadgeValidation.print),
  BadgeController.printBadge
);

/**
 * @swagger
 * /badges/{badgeId}:
 *   get:
 *     summary: Get a specific badge by ID
 *     tags: [Badges]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: badgeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Badge ID
 *     responses:
 *       200:
 *         description: Badge retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Badge retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     badge_id:
 *                       type: string
 *                       format: uuid
 *                     name:
 *                       type: string
 *                     status:
 *                       type: boolean
 *                     images:
 *                       type: array
 *                       items:
 *                         type: string
 *                         format: uri
 *                     content:
 *                       type: object
 *                     schema:
 *                       type: string
 *                       example: "patient"
 *                     key:
 *                       type: string
 *                       example: "patient_id"
 *                     variables:
 *                       type: array
 *                       items:
 *                         type: string
 *                       example: ["name", "patient_id", "date_of_birth"]
 *                     format:
 *                       type: string
 *                       enum: ["PNG", "JPEG", "JPG", "WEBP", "BMP", "TIFF", "PDF"]
 *                       example: "PNG"
 *                     updated_by:
 *                       type: string
 *                       format: uuid
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *       404:
 *         description: Badge not found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/:badgeId",
  auth("view_badge_details"),
  validate(BadgeValidation.badge),
  BadgeController.show
);

/**
 * @swagger
 * /badges:
 *   post:
 *     summary: Create a new badge
 *     tags: [Badges]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - schema
 *               - key
 *               - format
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 255
 *                 example: "Standard Employee Badge"
 *               status:
 *                 type: boolean
 *                 example: true
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: "Multiple image files (max 5)"
 *               content:
 *                 type: string
 *                 description: "JSON string of badge template content"
 *                 example: '{"canvasConfig": {"width": 900, "height": 1200}, "elements": []}'
 *               schema:
 *                 type: string
 *                 maxLength: 255
 *                 example: "patient"
 *               key:
 *                 type: string
 *                 maxLength: 255
 *                 example: "patient_id"
 *               format:
 *                 type: string
 *                 enum: ["PNG", "JPEG", "JPG", "WEBP", "BMP", "TIFF", "PDF"]
 *                 example: "PNG"
 *               updated_by:
 *                 type: string
 *                 format: uuid
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - schema
 *               - key
 *               - format
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 255
 *                 example: "Standard Employee Badge"
 *               status:
 *                 type: boolean
 *                 example: true
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uri
 *                 example: ["https://example.com/image1.jpg"]
 *               content:
 *                 type: object
 *                 example: {"layout": "standard", "fields": ["name", "photo", "id"]}
 *               schema:
 *                 type: string
 *                 maxLength: 255
 *                 example: "patient"
 *               key:
 *                 type: string
 *                 maxLength: 255
 *                 example: "patient_id"
 *               format:
 *                 type: string
 *                 enum: ["PNG", "JPEG", "JPG", "WEBP", "BMP", "TIFF", "PDF"]
 *                 example: "PNG"
 *               updated_by:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       201:
 *         description: Badge created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/",
  auth("create_badge"),
  uploadToBase64("images", { multiple: true, maxCount: 5 }),
  validate(BadgeValidation.create),
  BadgeController.create
);

/**
 * @swagger
 * /badges/{badgeId}:
 *   patch:
 *     summary: Update a badge
 *     tags: [Badges]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: badgeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Badge ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 255
 *               status:
 *                 type: boolean
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: "Multiple image files (max 5)"
 *               content:
 *                 type: string
 *                 description: "JSON string of badge template content"
 *               variables:
 *                 type: string
 *                 description: "JSON string array of variable names"
 *                 example: '["name", "patient_id", "date_of_birth"]'
 *               updated_by:
 *                 type: string
 *                 format: uuid
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 255
 *               status:
 *                 type: boolean
 *               content:
 *                 type: object
 *               variables:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["name", "patient_id", "date_of_birth"]
 *               updated_by:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Badge updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Badge not found
 *       401:
 *         description: Unauthorized
 */
router.patch(
  "/:badgeId",
  auth("update_badge"),
  uploadToBase64("images", { multiple: true, maxCount: 5 }),
  validate(BadgeValidation.update),
  BadgeController.update
);

/**
 * @swagger
 * /badges/{badgeId}:
 *   delete:
 *     summary: Delete a badge
 *     tags: [Badges]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: badgeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Badge ID
 *     responses:
 *       200:
 *         description: Badge deleted successfully
 *       404:
 *         description: Badge not found
 *       401:
 *         description: Unauthorized
 */
router.delete(
  "/:badgeId",
  auth("delete_badge"),
  validate(BadgeValidation.delete),
  BadgeController.delete
);



module.exports = router;
