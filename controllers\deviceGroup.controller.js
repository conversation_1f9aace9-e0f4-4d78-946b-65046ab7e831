const { KioskGroupSetting, KioskGroup, KioskSetting, MasterData, NdaTemplate } = require("../models");
const { paginate } = require("../models/plugins/paginate.plugin");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { status: httpStatus } = require("http-status");

/**
 * Get all kiosk settings (without pagination).
 *
 * @async
 * @function fetchKioskSettings
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a response with an array of kiosk setting data.
 */
exports.fetchKioskSettings = catchAsync(async (req, res, next) => {
  const kioskSettings = await KioskSetting.findAll({
    attributes: ["kiosk_setting_id", "config_key", "name", "description", "config_group", "default_config_value"],
    order: [['config_group', 'ASC'], ['name', 'ASC']],
  });

  sendSuccess(res, "Kiosk settings fetched successfully", httpStatus.OK, { data: kioskSettings });
});

/**
 * Get all kiosk group settings for a specific kiosk group (paginated).
 *
 * @async
 * @function index
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the kioskGroupId.
 * @param {Object} req.query - Query parameters for pagination (page and limit).
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a paginated response with kiosk group setting data, including kiosk setting details.
 */
exports.index = catchAsync(async (req, res) => {
  const { kioskGroupId } = req.params;
  const { page, limit } = req.query;
  const paginationOptions = { page, limit };

  const queryOptions = {
    order: [['updatedAt', 'DESC']],
    where: { kiosk_group_id: kioskGroupId },
    include: [
      {
        model: KioskGroup,
        as: "kiosk_group",
        attributes: ["name"],
      },
      {
        model: KioskSetting,
        as: "kiosk_setting",
        attributes: ["config_key", "name", "description", "config_group", "default_config_value"],
        include: [
          {
            model: MasterData,
            as: "masterdata",
            attributes: ["key", "value"],
            required: false,
          },
        ],
      },
    ],
  };

  const result = await paginate(KioskGroupSetting, queryOptions, paginationOptions);
  sendSuccess(res, "Device group settings retrieved successfully", httpStatus.OK, result);
});

/**
 * Get a single kiosk group setting by its ID for a specific kiosk group.
 *
 * @async
 * @function show
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains kioskGroupId and kioskGroupSettingId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the kiosk group setting details with kiosk setting information or a 404 error if not found.
 */
exports.show = catchAsync(async (req, res) => {
  const { kioskGroupId, kioskGroupSettingId } = req.params;
  const kioskGroupSetting = await KioskGroupSetting.findOne({
    where: { 
      kiosk_group_setting_id: kioskGroupSettingId, 
      kiosk_group_id: kioskGroupId 
    },
    include: [
      {
        model: KioskGroup,
        as: "kiosk_group",
        attributes: ["name"],
      },
      {
        model: KioskSetting,
        as: "kiosk_setting",
        attributes: ["config_key", "name", "description", "config_group", "default_config_value"],
        include: [
          {
            model: MasterData,
            as: "masterdata",
            attributes: ["key", "value"],
            required: false,
          },
        ],
      },
    ],
  });

  if (!kioskGroupSetting) {
    return sendError(res, "Device group setting not found", httpStatus.BAD_REQUEST);
  }

  sendSuccess(res, "Device group setting retrieved successfully", httpStatus.OK, kioskGroupSetting);
});

/**
 * Create multiple kiosk group settings for a specific kiosk group.
 *
 * @async
 * @function create
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the kioskGroupId.
 * @param {Object} req.body - Contains the settings array with kiosk group setting data.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the created kiosk group settings data.
 */
exports.create = catchAsync(async (req, res) => {
  const transaction = req.transaction;
  const { kioskGroupId } = req.params;
  const { settings } = req.body;

  // Get all unique kiosk_setting_ids that need default values
  const settingIdsNeedingDefaults = settings
    .filter(setting => !setting.config_value)
    .map(setting => setting.kiosk_setting_id);

  // Bulk fetch default values for settings that need them
  let defaultValues = {};
  if (settingIdsNeedingDefaults.length > 0) {
    const kioskSettings = await KioskSetting.findAll({
      where: { kiosk_setting_id: settingIdsNeedingDefaults },
      attributes: ["kiosk_setting_id", "default_config_value"],
    });

    defaultValues = kioskSettings.reduce((acc, setting) => {
      acc[setting.kiosk_setting_id] = setting.default_config_value || "";
      return acc;
    }, {});
  }

  // Prepare bulk create data
  const bulkCreateData = settings.map(setting => ({
    kiosk_group_id: kioskGroupId,
    kiosk_setting_id: setting.kiosk_setting_id,
    config_value: setting.config_value || defaultValues[setting.kiosk_setting_id] || "",
  }));

  // Bulk create all settings
  const createdSettings = await KioskGroupSetting.bulkCreate(bulkCreateData, {
    transaction,
    returning: true
  });

  const responseData = createdSettings.map(setting => ({
    kiosk_group_setting_id: setting.kiosk_group_setting_id,
    kiosk_setting_id: setting.kiosk_setting_id,
    config_value: setting.config_value,
  }));

  sendSuccess(res, "Device group settings created successfully", httpStatus.CREATED, {
    created_count: responseData.length,
    settings: responseData,
  });
});

/**
 * Update multiple kiosk group settings for a specific kiosk group.
 *
 * @async
 * @function update
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains kioskGroupId.
 * @param {Object} req.body - Contains the settings array with updated kiosk group setting data.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the updated kiosk group settings data or error if any setting not found.
 */
exports.update = catchAsync(async (req, res) => {
  const transaction = req.transaction;
  const { kioskGroupId } = req.params;
  const { settings } = req.body;

  const settingIds = settings.map(s => s.kiosk_group_setting_id);

  // Bulk fetch all settings to verify they exist and belong to the kiosk group
  const existingSettings = await KioskGroupSetting.findAll({
    where: {
      kiosk_group_setting_id: settingIds,
      kiosk_group_id: kioskGroupId,
    },
    attributes: ['kiosk_group_setting_id'],
  });

  // Check if all settings were found
  if (existingSettings.length !== settingIds.length) {
    const foundIds = existingSettings.map(s => s.kiosk_group_setting_id);
    const notFoundIds = settingIds.filter(id => !foundIds.includes(id));
    return sendError(res, `Device group settings not found: ${notFoundIds.join(', ')}`, httpStatus.BAD_REQUEST);
  }

  // Bulk update all settings
  const updatePromises = settings.map(setting =>
    KioskGroupSetting.update(
      { config_value: setting.config_value },
      {
        where: {
          kiosk_group_setting_id: setting.kiosk_group_setting_id,
          kiosk_group_id: kioskGroupId,
        },
        transaction,
      }
    )
  );

  await Promise.all(updatePromises);

  const responseData = settings.map(setting => ({
    kiosk_group_setting_id: setting.kiosk_group_setting_id,
    config_value: setting.config_value,
  }));

  sendSuccess(res, "Device group settings updated successfully", httpStatus.OK, {
    updated_count: responseData.length,
    settings: responseData,
  });
});

/**
 * Update a single kiosk group setting's details (for backward compatibility).
 *
 * @async
 * @function updateSingle
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains kioskGroupId and kioskGroupSettingId.
 * @param {Object} req.body - Contains the updated kiosk group setting data.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the updated kiosk group setting data or a 404 error if not found.
 */
exports.updateSingle = catchAsync(async (req, res) => {
  const transaction = req.transaction;
  const { kioskGroupId, kioskGroupSettingId } = req.params;
  const { config_value } = req.body;

  const kioskGroupSetting = await KioskGroupSetting.findOne({
    where: {
      kiosk_group_setting_id: kioskGroupSettingId,
      kiosk_group_id: kioskGroupId
    },
  });

  if (!kioskGroupSetting) {
    return sendError(res, "Device group setting not found", httpStatus.BAD_REQUEST);
  }

  await kioskGroupSetting.update({ config_value }, { transaction });

  sendSuccess(res, "Device group setting updated successfully", httpStatus.OK, {
    kiosk_group_setting_id: kioskGroupSettingId,
    config_value
  });
});

/**
 * Delete a kiosk group setting by ID.
 *
 * @async
 * @function remove
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains kioskGroupId and kioskGroupSettingId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success message or a 404 error if not found.
 */
exports.remove = catchAsync(async (req, res) => {
  const transaction = req.transaction;
  const { kioskGroupId, kioskGroupSettingId } = req.params;

  const kioskGroupSetting = await KioskGroupSetting.findOne({
    where: { 
      kiosk_group_setting_id: kioskGroupSettingId, 
      kiosk_group_id: kioskGroupId 
    },
  });

  if (!kioskGroupSetting) {
    return sendError(res, "Device group setting not found", httpStatus.BAD_REQUEST);
  }

  await kioskGroupSetting.destroy({ transaction });

  sendSuccess(res, "Device group setting deleted successfully", httpStatus.OK, {
    kiosk_group_setting_id: kioskGroupSettingId,
  });
});
