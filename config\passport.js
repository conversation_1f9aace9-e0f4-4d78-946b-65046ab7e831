const passport = require("passport");
const { Strategy: JwtStrategy, ExtractJwt } = require("passport-jwt");
const SamlStrategy = require("passport-saml").Strategy;
const OpenIDConnectStrategy = require("passport-openidconnect").Strategy;
const { OIDCStrategy: AzureOIDCStrategy } = require("passport-azure-ad");
const config = require("./config");
const { Identity } = require("../models");
const { tokenTypes } = require("./attributes");
const { validate } = require('uuid');

// ---------------------
// Email & Password Strategy
// ---------------------
const jwtOptions = {
  secretOrKey: config.jwt.secret,
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
};
const jwtVerify = async (payload, done) => {
  try {
    if (payload.type !== tokenTypes.ACCESS) {
      throw new Error("Invalid token type");
    }
    // const identity = await Identity.findByPk(payload.sub);
    // if (!identity) {
    //   return done(null, false);
    // }

    if (!(payload.sub && validate(payload.sub))) {
      return done(null, false);
    }
    const identity = {};
    identity.identity_id = payload.sub;

    // Add permissions from JWT payload to identity object for easy access
    identity.permissions = payload.permissions || [];

    done(null, identity);
  } catch (error) {
    done(error, false);
  }
};
passport.use("custom", new JwtStrategy(jwtOptions, jwtVerify));

// ---------------------
// SAML Strategy
// ---------------------
passport.use(
  "saml",
  new SamlStrategy(
    {
      path: config.saml.callbackUrl,
      entryPoint: config.saml.entryPoint,
      issuer: config.saml.issuer,
      cert: config.saml.cert,
    },
    async (profile, done) => {
      try {
        // Lookup the user by a unique attribute (e.g. email) from the SAML profile.
        let identity = await Identity.findOne({
          where: { email: profile.email },
        });
        if (!identity) {
          identity = await Identity.create({
            username: profile.nameID,
            email: profile.email,
            first_name: profile.firstName,
            last_name: profile.lastName,
          });
        }
        return done(null, identity);
      } catch (error) {
        return done(error, false);
      }
    }
  )
);

// ---------------------
// OpenID Connect Strategy
// ---------------------
passport.use(
  "oidc",
  new OpenIDConnectStrategy(
    {
      issuer: config.oidc.issuer,
      authorizationURL: config.oidc.authorizationURL,
      tokenURL: config.oidc.tokenURL,
      userInfoURL: config.oidc.userInfoURL,
      clientID: config.oidc.clientID,
      clientSecret: config.oidc.clientSecret,
      callbackURL: config.oidc.callbackURL,
    },
    async (issuer, sub, profile, accessToken, refreshToken, done) => {
      try {
        let identity = await Identity.findOne({
          where: { email: profile.email },
        });
        if (!identity) {
          identity = await Identity.create({
            username: profile.displayName || profile.email,
            email: profile.email,
            first_name: profile.given_name,
            last_name: profile.family_name,
          });
        }
        return done(null, identity);
      } catch (error) {
        return done(error, false);
      }
    }
  )
);

// ---------------------
// Azure AD Strategy (OIDC)
// ---------------------
passport.use(
  "azure",
  new AzureOIDCStrategy(
    {
      identityMetadata: config.azure.identityMetadata,
      clientID: config.azure.clientID,
      responseType: config.azure.responseType,
      responseMode: config.azure.responseMode,
      redirectUrl: config.azure.callbackURL,
      allowHttpForRedirectUrl: config.azure.allowHttpForRedirectUrl,
      clientSecret: config.azure.clientSecret,
    },
    async (iss, sub, profile, accessToken, refreshToken, done) => {
      try {
        // Note: The structure of the profile from Azure AD is found in profile._json.
        let identity = await Identity.findOne({
          where: { email: profile._json.email },
        });
        if (!identity) {
          identity = await Identity.create({
            username: profile.displayName || profile._json.email,
            email: profile._json.email,
            first_name: profile._json.given_name,
            last_name: profile._json.family_name,
          });
        }
        return done(null, identity);
      } catch (error) {
        return done(error, false);
      }
    }
  )
);

module.exports = passport;
