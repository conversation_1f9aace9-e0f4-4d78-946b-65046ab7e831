const logger = require('../config/logger');
const path = require("path");
const { transformWithDTL } = require("./others.helpers");
const { validateTransformedData_sendApiData<PERSON><PERSON><PERSON>, validateTransformedData_xmlHandler } = require('./dtl.helpers.js');

/**
 * Load mapping configuration for agent
 * @param {string} mappingName - Name of the mapping file
 * @returns {Object} Mapping configuration
 */
function loadMappingConfig(mappingName) {
  try {
    const mappingConfig = require(path.join(process.cwd(), `mappings/${mappingName}.mapping.json`));
    return mappingConfig;
  } catch (error) {
    logger.error(`[Agent Helper] Failed to load mapping config ${mappingName}:`, error);
    throw new Error(`Mapping configuration '${mappingName}' not found`);
  }
}

/**
 * Validate agent configuration
 * @param {Object} agent - Agent configuration
 * @returns {boolean} Validation result
 */
function validateAgentConfig(agent) {
  const requiredFields = ['name', 'type', 'source', 'queue', 'handler'];

  for (const field of requiredFields) {
    if (!agent[field]) {
      logger.error(`[Agent Helper] Agent validation failed: missing ${field}`);
      return false;
    }
  }

  if (!['Inbound', 'Outbound'].includes(agent.type)) {
    logger.error(`[Agent Helper] Agent validation failed: invalid type ${agent.type}`);
    return false;
  }

  return true;
}

/**
 * Log agent activity with consistent format
 * @param {string} agentName - Agent name
 * @param {string} activity - Activity description
 * @param {Object} metadata - Additional metadata
 */
function logAgentActivity(agentName, activity, metadata = {}) {
  logger.info(`[Agent: ${agentName}] ${activity}`, {
    agentName,
    timestamp: new Date().toISOString(),
    ...metadata
  });
}

function transform_CSVHandler(input, dtlTemplate) {
  try {
    const transformed = transformWithDTL(input, dtlTemplate);

    //convert undefined values to empty string
    Object.values(transformed).forEach(record => {
      for (const [key, value] of Object.entries(record)) {
        if (value === undefined) {
          record[key] = '';
        }
      }
    });

    return Object.values(transformed)[0];
  } catch (err) {
    throw new Error(`CSV Transformation failed: ${err.message}`);
  }
}

function transform_SendAPIDataHandler(input, dtlTemplate, validations) {
  try {
    const transformed = transformWithDTL(input, dtlTemplate);

    if (!transformed || !transformed.dtlTransformation) {
      throw new Error(`API Data Transformation failed: No dtlTransformation found in transformed data`);
    }

    //convert all the undefined values to empty string
    transformed.dtlTransformation = replaceUndefinedWithEmptyString(transformed.dtlTransformation);

    const validationResult = validateTransformedData_sendApiDataHandler(transformed.dtlTransformation.properties, validations);
    const transformedData = transformed.dtlTransformation.properties;
    const transformedApiConfig = transformed.apiConfig;

    return {
      transformed: { data: transformedData, apiConfig: transformedApiConfig },
      validationResult
    };
  } catch (err) {
    throw new Error(`API Data Transformation failed: ${err.message}`);
  }
}

function transform_XMLHandler(input, template, validations) {
  try {
    const transformed = transformWithDTL(input, template);

    if (!transformed || !transformed.dtlTransformation) {
      throw new Error(`XML Data Transformation failed: No dtlTransformation found in transformed data`);
    }
    const { Data : transformedData, MetaData : transformedMetaData } = transformed.dtlTransformation;

    if (!transformedData) {
      throw new Error(`XML Data Transformation failed: No transformedData found in transformed data`);
    }
    if (!transformedMetaData) {
      throw new Error(`XML Data Transformation failed: No transformedMetaData found in transformed data`);
    }

    //convert all the undefined values to empty string
    transformed.dtlTransformation = replaceUndefinedWithEmptyString(transformed.dtlTransformation);

    const validationResult = validateTransformedData_xmlHandler(transformedData, validations);

    return {
      transformed: transformed.dtlTransformation,
      validationResult,
      xmlConfig:transformed.xmlConfig
    };
  } catch (err) {
    throw new Error(`XML Data Transformation failed: ${err.message}`);
  }
}

module.exports = {
  loadMappingConfig,
  validateAgentConfig,
  logAgentActivity,
  transform_CSVHandler,
  transform_SendAPIDataHandler,
  transform_XMLHandler
};


const replaceUndefinedWithEmptyString = (obj) => {
  if (Array.isArray(obj)) {
    return obj.map(item => replaceUndefinedWithEmptyString(item));
  }

  if (obj && typeof obj === 'object') {
    const newObj = {};
    for (const [key, value] of Object.entries(obj)) {
      if (key === 'timestamp' && (value === undefined || value === '')) {
        newObj[key] = new Date().toISOString();
      } else if (value === undefined) {
        newObj[key] = '';
      } else {
        newObj[key] = replaceUndefinedWithEmptyString(value);
      }
    }
    return newObj;
  }

  return obj;
};

