const { getCachedMasterDataValue } = require("./caching.helper");
const models = require("../models");

module.exports = function (parseHL7Date) {
    return {
        applyHL7DateParsing: function (obj) {
            const dateKeywords = ['date', 'dob', 'timestamp'];

            for (const sectionKey in obj) {
                if (!obj.hasOwnProperty(sectionKey)) continue;

                const section = obj[sectionKey];
                if (typeof section !== 'object' || section === null) continue;

                for (const fieldKey in section) {
                    if (!section.hasOwnProperty(fieldKey)) continue;

                    const value = section[fieldKey];

                    if (
                        typeof value === 'string' &&
                        value != '' &&
                        dateKeywords.some(keyword => fieldKey.toLowerCase().includes(keyword))
                        && !value.includes('-') && !value.includes('/')
                    ) {
                        section[fieldKey] = parseHL7Date(value);
                    }
                }
            }

            return obj;
        },

        resolveMasterDataFields: async function (transformed, groupMap) {
            for (const [modelName, fields] of Object.entries(groupMap)) {
                if (!transformed[modelName]) continue;

                for (const [field, group] of Object.entries(fields)) {
                    const raw = transformed[modelName][field];

                    if (raw !== undefined && raw !== null) {
                        const key = await getCachedMasterDataValue(group, raw);
                        if (key !== null) {
                            transformed[modelName][field] = key;
                        } else {
                            // fallback to model default (if any)
                            const attr = models[modelName]?.rawAttributes[field];
                            if (attr) {
                                transformed[modelName][field] =
                                    attr.defaultValue ?? attr.default ?? raw;
                            }
                        }
                    }
                }
            }

            return transformed;
        },
        cleanDTLResult: cleanDTLResult,
    }
}
/*sendAPIDataHandler Transformations Helpers*/
module.exports.validateTransformedData_sendApiDataHandler = function (data, validation) {
    const errors = [];

    if (!validation) {
        return { isValid: true, errors: [] };
    }

    // Check required fields
    if (validation.required) {
        for (const field of validation.required) {
            const fieldValue = getNestedValue(data, field);
            if (fieldValue === undefined || fieldValue === null || fieldValue === '') {
                errors.push(`Required field ${field} is missing`);
            }
        }
    }

    // Check field rules
    if (validation.rules) {
        for (const [fieldPath, rules] of Object.entries(validation.rules)) {
            const fieldValue = getNestedValue(data, fieldPath);

            if (rules.required && (fieldValue === undefined || fieldValue === null || fieldValue === '')) {
                errors.push(`Required field ${fieldPath} is missing`);
                continue;
            }

            if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
                // Type validation
                if (rules.type === 'email' && !isValidEmail(fieldValue)) {
                    errors.push(`Field ${fieldPath} must be a valid email address`);
                }

                if (rules.type === 'string' && typeof fieldValue !== 'string') {
                    errors.push(`Field ${fieldPath} must be a string`);
                }

                // Length validation
                if (rules.minLength && String(fieldValue).length < rules.minLength) {
                    errors.push(`Field ${fieldPath} must be at least ${rules.minLength} characters long`);
                }

                if (rules.maxLength && String(fieldValue).length > rules.maxLength) {
                    errors.push(`Field ${fieldPath} must be no more than ${rules.maxLength} characters long`);
                }
            }
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

module.exports.validateTransformedData_xmlHandler = function (transformedData, validations) {
    try {
        const errors = [];

        if (!validations) {
            return { isValid: true, errors: [] };
        }

        const { required = [], rules = [] } = validations;

        const getRecords = (modelName) => {
            const data = transformedData[modelName];
            return Array.isArray(data) ? data : [];
        };

        const validateField = (value, rule, modelName, index) => {
            const path = `${modelName}[${index}].${rule.value}`;

            if (rule.required && (value === undefined || value === null || value === "")) {
                errors.push(`${path} is required`);
                return;
            }

            if (rule.type === "string" && typeof value !== "string") {
                errors.push(`${path} must be a string`);
            }

            if (rule.type === "string" && rule.minLength && typeof value === "string" && value.length < rule.minLength) {
                errors.push(`${path} must be at least ${rule.minLength} characters`);
            }

            if (rule.type === "email" && typeof value === "string") {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    errors.push(`${path} is not a valid email`);
                }
            }
        };

        // Validate required fields
        for (const req of required) {
            const records = getRecords(req.model);
            records.forEach((record, i) => {
                const val = record[req.value];
                if (val === undefined || val === null || val === "") {
                    errors.push(`${req.model}[${i}].${req.value} is required`);
                }
            });
        }
        
        // Apply validation rules
        for (const rule of rules) {
            const records = getRecords(rule.model);
            records.forEach((record, i) => {
                const val = record[rule.value];
                validateField(val, rule, rule.model, i);
            });
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    } catch (err) {
        return {
            isValid: false,
            errors: [err.message]
        }
    }
}

/*Helper Utilities*/
function cleanDTLResult(obj, typeOfFunction) {

    const result = {};
    for (const [key, value] of Object.entries(obj)) {
        if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
            const nested = cleanDTLResult(value, typeOfFunction);
            if (Object.keys(nested).length > 0) result[key] = nested;
        } else if (isValidKey(value)) {
            result[key] = value;
        }
    }
    return result;


    function isValidKey(value) {
        let conditionToDisRegardKey = ![null, undefined, ''].includes(value);
        if (typeOfFunction == 'parseData')
            conditionToDisRegardKey = ![null, undefined].includes(value);
        return conditionToDisRegardKey;
    }
}

const getNestedValue = (obj, path) => {
    const parts = path.split('.');
    let value = obj;

    for (const part of parts) {
        if (value && typeof value === 'object' && part in value) {
            value = value[part];
        } else {
            return undefined;
        }
    }

    return value;
};


const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}