// middleware/uploadToBase64.js
const multer = require("multer");

const upload = multer({ storage: multer.memoryStorage() });

function uploadToBase64(fieldName, options = {}) {
  const { multiple = false, maxCount = 10 } = options;

  let handler;
  if (multiple) {
    handler = upload.array(fieldName, maxCount);
  } else {
    handler = upload.single(fieldName);
  }

  return (req, res, next) => {
    handler(req, res, (err) => {
      if (err) return next(err);

      if (multiple && req.files && req.files.length > 0) {
        // Handle multiple files
        req.body[fieldName] = req.files.map(file => {
          const mimeType = file.mimetype;
          const base64 = file.buffer.toString("base64");
          return `data:${mimeType};base64,${base64}`;
        });
      } else if (!multiple && req.file) {
        // Handle single file
        const mimeType = req.file.mimetype;
        const base64 = req.file.buffer.toString("base64");
        req.body[fieldName] = `data:${mimeType};base64,${base64}`;
      }
      next();
    });
  };
}

module.exports = uploadToBase64;
